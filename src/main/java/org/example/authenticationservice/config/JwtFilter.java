package org.example.authenticationservice.config;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.BadRequestException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.AllUserDetails;
import org.example.authenticationservice.model.enumType.TokenType;
import org.example.authenticationservice.service.JwtService;
import org.example.authenticationservice.service.UserService;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseCookie;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.*;

@Slf4j
@RequiredArgsConstructor
@Component
public class JwtFilter extends OncePerRequestFilter {

    private static final Set<String> ALLOWED_PATHS = Set.of("/login", "/register", "/logout", "/auth/google");

    private final JwtService jwtService;
    private final UserService userService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        if (ALLOWED_PATHS.contains(request.getRequestURI())) {
            filterChain.doFilter(request, response);
            return;
        }

        Cookie[] cookies = request.getCookies();
        if (cookies == null || cookies.length == 0) {
            filterChain.doFilter(request, response);
            return;
        }

        String token = Arrays.stream(cookies).filter(cookie -> cookie.getName().equals(TokenType.ACCESS_TOKEN.name()))
                .findFirst().map(Cookie::getValue).orElse(null);

        AllUserDetails user = userService.getAllUserDetailsById(jwtService.getUserIdFromToken(token))
                .orElseThrow(() -> new BadRequestException("User not found"));

        if (jwtService.isNotValidToken(token))
            refreshToken(cookies, user, request, response, filterChain);

        setupSecurityContext(request, user);
        filterChain.doFilter(request, response);
    }

    private static void setupSecurityContext(HttpServletRequest request, AllUserDetails user) {
        Collection<GrantedAuthority> authorities = user.getAuthorities();
        UsernamePasswordAuthenticationToken authentication =
                new UsernamePasswordAuthenticationToken(user, null, authorities);

        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
        SecurityContextHolder.getContext().setAuthentication(authentication);
    }

    private void refreshToken(Cookie[] cookies, AllUserDetails userDetails, HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        final String refreshToken = Arrays.stream(cookies).filter(cookie -> cookie.getName().equals(TokenType.REFRESH_TOKEN.name()))
                .findFirst().map(Cookie::getValue).orElse(null);

        if (jwtService.isNotValidToken(refreshToken)) {
            filterChain.doFilter(request, response);
            return;
        }

        String token = jwtService.generateAccessToken(userDetails);

        ResponseCookie accessCookie = jwtService.tokenToCookie(token, TokenType.ACCESS_TOKEN);
        ResponseCookie refreshCookie = jwtService.tokenToCookie(refreshToken, TokenType.REFRESH_TOKEN);

        response.addHeader(HttpHeaders.SET_COOKIE, accessCookie.toString());
        response.addHeader(HttpHeaders.SET_COOKIE, refreshCookie.toString());

    }
}
