package org.example.authenticationservice.controller;

import jakarta.validation.Valid;
import org.example.AllUserDetails;
import org.example.authenticationservice.dto.LoginDto;
import org.example.authenticationservice.dto.RegisterDto;
import org.example.authenticationservice.service.AuthService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static org.example.authenticationservice.config.Constants.BASE_URL;

@RestController
@RequestMapping(BASE_URL + "/auth")
public class AuthenticationController {

    private final AuthService authService;

    public AuthenticationController(AuthService authService) {
        this.authService = authService;
    }

    @PostMapping("/login")
    public ResponseEntity<?> login(@RequestBody @Valid LoginDto registerDto) {
        return authService.login(registerDto.username(), registerDto.password());
    }

    @PostMapping("/logout")
    public ResponseEntity<?> logout() {
        return authService.logout();
    }

    @PostMapping("/register")
    public ResponseEntity<?> register(@RequestBody @Valid RegisterDto registerDto) {
        return authService.register(registerDto.username(), registerDto.password());
    }
}
