package org.example.authenticationservice.grpc;

import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.AllUserDetails;
import org.example.authenticationservice.grpc.UserServiceGrpc;
import org.example.authenticationservice.grpc.UserServiceProto.*;
import org.example.authenticationservice.service.UserService;
import org.springframework.grpc.server.service.GrpcService;
import org.springframework.security.core.GrantedAuthority;

import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class UserGrpcService extends UserServiceGrpc.UserServiceImplBase {

    private final UserService userService;

    @Override
    public void loadUserByUsername(LoadUserByUsernameRequest request,
                                   StreamObserver<LoadUserByUsernameResponse> responseObserver) {
        try {
            log.info("Loading user by username: {}", request.getUsername());

            AllUserDetails userDetails = userService.loadUserByUsername(request.getUsername());

            LoadUserByUsernameResponse.Builder responseBuilder = LoadUserByUsernameResponse.newBuilder();

            if (userDetails != null) {
                UserDetails grpcUserDetails = convertToGrpcUserDetails(userDetails);
                responseBuilder
                    .setSuccess(true)
                    .setMessage("User found successfully")
                    .setUserDetails(grpcUserDetails);
            } else {
                responseBuilder
                    .setSuccess(false)
                    .setMessage("User not found");
            }

            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();

        } catch (Exception e) {
            log.error("Error loading user by username: {}", request.getUsername(), e);
            LoadUserByUsernameResponse response = LoadUserByUsernameResponse.newBuilder()
                .setSuccess(false)
                .setMessage("Error loading user: " + e.getMessage())
                .build();

            responseObserver.onNext(response);
            responseObserver.onCompleted();
        }
    }

    @Override
    public void getUserDetailsById(GetUserDetailsByIdRequest request,
                                   StreamObserver<GetUserDetailsByIdResponse> responseObserver) {
        try {
            log.info("Getting user details by ID: {}", request.getUserId());

            Optional<AllUserDetails> userDetailsOpt = userService.getAllUserDetailsById(request.getUserId());

            GetUserDetailsByIdResponse.Builder responseBuilder = GetUserDetailsByIdResponse.newBuilder();

            if (userDetailsOpt.isPresent()) {
                UserDetails grpcUserDetails = convertToGrpcUserDetails(userDetailsOpt.get());
                responseBuilder
                    .setSuccess(true)
                    .setMessage("User found successfully")
                    .setUserDetails(grpcUserDetails);
            } else {
                responseBuilder
                    .setSuccess(false)
                    .setMessage("User not found");
            }

            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();

        } catch (Exception e) {
            log.error("Error getting user details by ID: {}", request.getUserId(), e);
            GetUserDetailsByIdResponse response = GetUserDetailsByIdResponse.newBuilder()
                .setSuccess(false)
                .setMessage("Error getting user details: " + e.getMessage())
                .build();

            responseObserver.onNext(response);
            responseObserver.onCompleted();
        }
    }

    @Override
    public void registerUser(RegisterUserRequest request,
                             StreamObserver<RegisterUserResponse> responseObserver) {
        try {
            log.info("Registering user: {}", request.getUsername());

            String userId = userService.register(request.getUsername(), request.getPassword());

            RegisterUserResponse response = RegisterUserResponse.newBuilder()
                .setSuccess(true)
                .setMessage("User registered successfully")
                .setUserId(userId)
                .build();

            responseObserver.onNext(response);
            responseObserver.onCompleted();

        } catch (Exception e) {
            log.error("Error registering user: {}", request.getUsername(), e);
            RegisterUserResponse response = RegisterUserResponse.newBuilder()
                .setSuccess(false)
                .setMessage("Error registering user: " + e.getMessage())
                .build();

            responseObserver.onNext(response);
            responseObserver.onCompleted();
        }
    }

    private UserDetails convertToGrpcUserDetails(AllUserDetails allUserDetails) {
        return UserDetails.newBuilder()
            .setId(allUserDetails.getId().toString())
            .setUsername(allUserDetails.getUsername())
            .setPassword(allUserDetails.getPassword())
            .addAllAuthorities(allUserDetails.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList()))
            .setAccountNonExpired(allUserDetails.isAccountNonExpired())
            .setAccountNonLocked(allUserDetails.isAccountNonLocked())
            .setCredentialsNonExpired(allUserDetails.isCredentialsNonExpired())
            .setEnabled(allUserDetails.isEnabled())
            .build();
    }
}
