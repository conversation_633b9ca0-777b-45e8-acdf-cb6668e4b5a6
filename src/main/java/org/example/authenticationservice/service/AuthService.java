package org.example.authenticationservice.service;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.ws.rs.BadRequestException;
import lombok.RequiredArgsConstructor;
import org.example.AllUserDetails;
import org.example.authenticationservice.model.enumType.TokenType;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseCookie;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class AuthService {
    private final JwtService jwtService;
    private final UserService userService;

    public ResponseEntity<?> login(@NotBlank String username, @NotBlank String password) {
        AllUserDetails userDetails = userService.loadUserByUsername(username);

        if (userDetails == null){
            throw new BadRequestException("User not found");
        }

        if (!userDetails.getPassword().equals(password)) {
            throw new BadRequestException("Invalid password");
        }

        String accessToken = jwtService.generateAccessToken(userDetails);
        String refreshToken = jwtService.generateRefreshToken(userDetails);

        return ResponseEntity.ok()
                .header(HttpHeaders.SET_COOKIE, jwtService.tokenToCookie(accessToken, TokenType.ACCESS_TOKEN).toString())
                .header(HttpHeaders.SET_COOKIE, jwtService.tokenToCookie(refreshToken, TokenType.REFRESH_TOKEN).toString())
                .body("Login successful");
    }

    public ResponseEntity<?> logout() {
        ResponseCookie accessCookie = jwtService.tokenToCookie("", TokenType.ACCESS_TOKEN);
        ResponseCookie refreshCookie = jwtService.tokenToCookie("", TokenType.REFRESH_TOKEN);

        return ResponseEntity.ok().header(HttpHeaders.SET_COOKIE, accessCookie.toString()).header(HttpHeaders.SET_COOKIE, refreshCookie.toString()).body("Logged out");
    }

    public ResponseEntity<?> register(@Pattern(EMAIL_REGEX) String username, String password) {
        userService.register(username, password);
        return ResponseEntity.ok().body("Registered");
    }
}
