package org.example.authenticationservice.service;

import jakarta.validation.Valid;
import jakarta.ws.rs.BadRequestException;
import lombok.RequiredArgsConstructor;
import org.example.AllUserDetails;
import org.example.authenticationservice.model.enumType.TokenType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseCookie;
import org.springframework.security.oauth2.jwt.*;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;
import java.util.Objects;

@Service
@RequiredArgsConstructor
public class JwtService {
    private final JwtEncoder encoder;
    private final JwtDecoder decoder;

    @Value("${jwt.access-token.expiry:PT1H}")
    private Duration accessTokenExpiry;

    @Value("${jwt.refresh-token.expiry:P7D}")
    private Duration refreshTokenExpiry;

    @Value("${jwt.issuer:https://example.com}")
    private String issuer;

    public String generateAccessToken(@Valid AllUserDetails user) {
        Instant now = Instant.now();

        JwtClaimsSet claims = JwtClaimsSet.builder()
                .issuer(issuer)
                .issuedAt(now)
                .expiresAt(now.plus(accessTokenExpiry))
                .subject(String.valueOf(user.getId()))
                .claim("type", TokenType.ACCESS_TOKEN.name())
                .claim("roles", user.getAuthorities())
                .build();

        return this.encoder.encode(JwtEncoderParameters.from(claims)).getTokenValue();
    }

    public String generateRefreshToken(@Valid AllUserDetails user) {
        Instant now = Instant.now();

        JwtClaimsSet claims = JwtClaimsSet.builder()
                .issuer(issuer)
                .issuedAt(now)
                .expiresAt(now.plus(refreshTokenExpiry))
                .subject(String.valueOf(user.getId()))
                .claim("type", TokenType.REFRESH_TOKEN.name())
                .build();

        return this.encoder.encode(JwtEncoderParameters.from(claims)).getTokenValue();
    }

    public boolean isNotValidToken(String token) {
        try {
            Jwt jwt = decoder.decode(token);
            return Objects.requireNonNull(jwt.getExpiresAt()).isBefore(Instant.now());
        } catch (Exception e) {
            return false;
        }
    }

    public String getUserIdFromToken(String token) throws JwtException {
        try {
            Jwt jwt = decoder.decode(token);
            String userId = jwt.getSubject();

            if (userId == null || userId.isEmpty()) {
                throw new JwtException("userId is null or empty");
            }

            return userId;
        } catch (JwtException e) {
            throw e;
        } catch (Exception e) {
            throw new JwtException("exception while parsing token: " + e.getMessage(), e);
        }
    }

    public ResponseCookie tokenToCookie(String token, TokenType type) {
        if (type == TokenType.ACCESS_TOKEN)
            return ResponseCookie.from(type.toString(), token).httpOnly(true).path("/").maxAge(accessTokenExpiry).sameSite("Strict").build();
        else if (type == TokenType.REFRESH_TOKEN)
            return ResponseCookie.from(type.toString(), token).httpOnly(true).path("/").maxAge(refreshTokenExpiry).sameSite("Strict").build();
        else
            throw new BadRequestException("Unknown token type");
    }
}
