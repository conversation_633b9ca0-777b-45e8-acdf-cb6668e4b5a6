package org.example.authenticationservice.service;

import jakarta.validation.constraints.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.AllUserDetails;
import org.example.authenticationservice.model.User;
import org.example.authenticationservice.repository.UserRepository;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static org.example.authenticationservice.config.Constants.EMAIL_REGEX;
import static org.example.authenticationservice.config.Constants.PASSWORD_REGEX;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserService implements UserDetailsService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    @Override
    public AllUserDetails loadUserByUsername(@Pattern(regexp = EMAIL_REGEX) String username) throws UsernameNotFoundException {
        log.info("Loading user by username: {}", username);

        Optional<User> userOpt = userRepository.findByUsername(username);
        if (userOpt.isEmpty()) {
            log.warn("User not found: {}", username);
            throw new UsernameNotFoundException("User not found: " + username);
        }

        User user = userOpt.get();
        return convertToAllUserDetails(user);
    }

    public Optional<AllUserDetails> getAllUserDetailsById(String userId) {
        log.info("Getting user details by ID: {}", userId);

        Optional<User> userOpt = userRepository.findById(userId);
        return userOpt.map(this::convertToAllUserDetails);
    }

    public String register(@Pattern(regexp = EMAIL_REGEX) String username, @Pattern(regexp = PASSWORD_REGEX) String password) {
        log.info("Registering user: {}", username);

        if (userRepository.existsByUsername(username)) {
            throw new IllegalArgumentException("Username already exists: " + username);
        }

        String encodedPassword = passwordEncoder.encode(password);
        User user = new User(username, encodedPassword);
        User savedUser = userRepository.save(user);

        log.info("User registered successfully with ID: {}", savedUser.getId());
        return savedUser.getId();
    }

    private AllUserDetails convertToAllUserDetails(User user) {
        Collection<GrantedAuthority> authorities = user.getAuthorities().stream()
            .map(SimpleGrantedAuthority::new)
            .collect(Collectors.toList());

        return new AllUserDetails() {
            @Override
            public String getId() {
                return user.getId();
            }

            @Override
            public Collection<? extends GrantedAuthority> getAuthorities() {
                return authorities;
            }

            @Override
            public String getPassword() {
                return user.getPassword();
            }

            @Override
            public String getUsername() {
                return user.getUsername();
            }

            @Override
            public boolean isAccountNonExpired() {
                return user.isAccountNonExpired();
            }

            @Override
            public boolean isAccountNonLocked() {
                return user.isAccountNonLocked();
            }

            @Override
            public boolean isCredentialsNonExpired() {
                return user.isCredentialsNonExpired();
            }

            @Override
            public boolean isEnabled() {
                return user.isEnabled();
            }
        };
    }
}
