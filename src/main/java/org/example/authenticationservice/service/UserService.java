package org.example.authenticationservice.service;

import org.example.AllUserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class UserService implements UserDetailsService {
    @Override
    public AllUserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        return null;
    }

    public Optional<AllUserDetails> getAllUserDetailsById(String userIdFromToken) {
        return Optional.empty();
    }

    public void register(String username, String password) {
    }
}
