syntax = "proto3";

package org.example.authenticationservice;

option java_package = "org.example.authenticationservice.grpc";
option java_outer_classname = "UserServiceProto";

// User service definition
service UserService {
  // Load user by username (for authentication)
  rpc LoadUserByUsername(LoadUserByUsernameRequest) returns (LoadUserByUsernameResponse);
  
  // Get user details by ID
  rpc GetUserDetailsById(GetUserDetailsByIdRequest) returns (GetUserDetailsByIdResponse);
  
  // Register a new user
  rpc RegisterUser(RegisterUserRequest) returns (RegisterUserResponse);
}

// Request messages
message LoadUserByUsernameRequest {
  string username = 1;
}

message GetUserDetailsByIdRequest {
  string user_id = 1;
}

message RegisterUserRequest {
  string username = 1;
  string password = 2;
}

// Response messages
message LoadUserByUsernameResponse {
  bool success = 1;
  string message = 2;
  UserDetails user_details = 3;
}

message GetUserDetailsByIdResponse {
  bool success = 1;
  string message = 2;
  UserDetails user_details = 3;
}

message RegisterUserResponse {
  bool success = 1;
  string message = 2;
  string user_id = 3;
}

// Data transfer objects
message UserDetails {
  string id = 1;
  string username = 2;
  string password = 3;
  repeated string authorities = 4;
  bool account_non_expired = 5;
  bool account_non_locked = 6;
  bool credentials_non_expired = 7;
  bool enabled = 8;
}
