package org.example.authenticationservice.grpc;

import io.grpc.stub.StreamObserver;
import org.example.AllUserDetails;
import org.example.authenticationservice.grpc.UserServiceProto.*;
import org.example.authenticationservice.service.UserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UserGrpcServiceTest {

    @Mock
    private UserService userService;

    @Mock
    private StreamObserver<LoadUserByUsernameResponse> loadUserResponseObserver;

    @Mock
    private StreamObserver<RegisterUserResponse> registerResponseObserver;

    private UserGrpcService userGrpcService;

    @BeforeEach
    void setUp() {
        userGrpcService = new UserGrpcService(userService);
    }

    @Test
    void testLoadUserByUsername_Success() {
        // Given
        String username = "<EMAIL>";
        UUID userId = UUID.randomUUID();
        AllUserDetails mockUserDetails = createMockAllUserDetails(userId, username);
        
        when(userService.loadUserByUsername(username)).thenReturn(mockUserDetails);

        LoadUserByUsernameRequest request = LoadUserByUsernameRequest.newBuilder()
            .setUsername(username)
            .build();

        // When
        userGrpcService.loadUserByUsername(request, loadUserResponseObserver);

        // Then
        ArgumentCaptor<LoadUserByUsernameResponse> responseCaptor = 
            ArgumentCaptor.forClass(LoadUserByUsernameResponse.class);
        
        verify(loadUserResponseObserver).onNext(responseCaptor.capture());
        verify(loadUserResponseObserver).onCompleted();

        LoadUserByUsernameResponse response = responseCaptor.getValue();
        assertTrue(response.getSuccess());
        assertEquals("User found successfully", response.getMessage());
        assertEquals(userId.toString(), response.getUserDetails().getId());
        assertEquals(username, response.getUserDetails().getUsername());
    }

    @Test
    void testRegisterUser_Success() {
        // Given
        String username = "<EMAIL>";
        String password = "password123";
        String userId = UUID.randomUUID().toString();
        
        when(userService.register(username, password)).thenReturn(userId);

        RegisterUserRequest request = RegisterUserRequest.newBuilder()
            .setUsername(username)
            .setPassword(password)
            .build();

        // When
        userGrpcService.registerUser(request, registerResponseObserver);

        // Then
        ArgumentCaptor<RegisterUserResponse> responseCaptor = 
            ArgumentCaptor.forClass(RegisterUserResponse.class);
        
        verify(registerResponseObserver).onNext(responseCaptor.capture());
        verify(registerResponseObserver).onCompleted();

        RegisterUserResponse response = responseCaptor.getValue();
        assertTrue(response.getSuccess());
        assertEquals("User registered successfully", response.getMessage());
        assertEquals(userId, response.getUserId());
    }

    @Test
    void testLoadUserByUsername_UserNotFound() {
        // Given
        String username = "<EMAIL>";
        
        when(userService.loadUserByUsername(username)).thenReturn(null);

        LoadUserByUsernameRequest request = LoadUserByUsernameRequest.newBuilder()
            .setUsername(username)
            .build();

        // When
        userGrpcService.loadUserByUsername(request, loadUserResponseObserver);

        // Then
        ArgumentCaptor<LoadUserByUsernameResponse> responseCaptor = 
            ArgumentCaptor.forClass(LoadUserByUsernameResponse.class);
        
        verify(loadUserResponseObserver).onNext(responseCaptor.capture());
        verify(loadUserResponseObserver).onCompleted();

        LoadUserByUsernameResponse response = responseCaptor.getValue();
        assertFalse(response.getSuccess());
        assertEquals("User not found", response.getMessage());
    }

    private AllUserDetails createMockAllUserDetails(UUID id, String username) {
        Collection<GrantedAuthority> authorities = List.of(new SimpleGrantedAuthority("ROLE_USER"));
        
        return new AllUserDetails(
            id,
            username,
            "encodedPassword",
            username, // email
            authorities
        );
    }
}
